#!/bin/bash

# 考试系统Docker启动脚本

set -e

echo "🚀 启动考试系统..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 进入docker目录
cd "$(dirname "$0")"

# 检查并创建必要的目录
echo "🔍 检查MySQL目录结构..."

# 创建MySQL初始化目录
if [ ! -d "mysql/init" ]; then
    echo "📁 创建MySQL初始化目录: mysql/init"
    mkdir -p mysql/init
fi

# 创建MySQL数据目录
if [ ! -d "mysql/data" ]; then
    echo "📁 创建MySQL数据目录: mysql/data"
    mkdir -p mysql/data
    # 设置正确的权限（MySQL需要999:999）
    sudo chown -R 999:999 mysql/data 2>/dev/null || echo "⚠️  无法设置MySQL数据目录权限，请确保Docker有足够权限"
fi

# 检查初始化脚本
if [ ! -f "mysql/init/01-exam.sql" ]; then
    echo "📄 复制初始化脚本..."
    if [ -f "../docs/exam.sql" ]; then
        cp ../docs/exam.sql mysql/init/01-exam.sql
        echo "✅ 已复制考试系统初始化脚本"
    else
        echo "⚠️  未找到 docs/exam.sql 文件"
    fi
fi

echo "📋 MySQL目录结构："
ls -la mysql/

# 检查镜像是否存在
if ! docker images | grep -q "exam"; then
    echo "📦 镜像不存在，开始构建..."
    ./build.sh
fi

echo "🔧 启动所有服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 10

echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 考试系统启动完成！"
echo ""
echo "📋 访问地址："
echo "   前端地址: http://localhost"
echo "   后端API: http://localhost/exam/api"
echo "   图片访问: http://localhost/upload/file/"
echo "   MySQL: localhost:3306"
echo ""
echo "📋 管理命令："
echo "   查看日志: ./logs.sh"
echo "   停止服务: ./stop.sh"
echo "   重启服务: docker-compose restart"
echo ""
echo "🔍 服务健康检查："
echo "   docker-compose ps"
