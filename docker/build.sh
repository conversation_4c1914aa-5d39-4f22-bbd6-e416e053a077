#!/bin/bash

# 考试系统Docker构建脚本

set -e

echo "🚀 开始构建考试系统Docker镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 进入docker目录
cd "$(dirname "$0")"

echo "📦 构建MySQL容器..."
echo "✅ MySQL使用官方镜像，无需构建"

echo "📦 构建Spring Boot后端容器..."
docker-compose build exam-api

echo "📦 构建Vue.js前端容器..."
docker-compose build exam-vue

echo "📦 构建Nginx反向代理容器..."
docker-compose build nginx

echo "🎉 所有镜像构建完成！"
echo ""
echo "📋 构建的镜像："
docker images | grep -E "(exam|mysql)" | head -10

echo ""
echo "🚀 使用以下命令启动服务："
echo "   ./start.sh"
