services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: exam-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: exam
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
      TZ: Asia/Shanghai
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - exam-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Spring Boot后端
  exam-api:
    build:
      context: ..
      dockerfile: docker/backend/Dockerfile
    container_name: exam-api
    restart: unless-stopped
    environment:
      # 数据库配置覆盖（零代码修改方案）
      SPRING_DATASOURCE_URL: "*******************************************************************************************************************************************"
      SPRING_DATASOURCE_USERNAME: "root"
      SPRING_DATASOURCE_PASSWORD: "123456"
      
      # 文件上传配置覆盖
      CONF_UPLOAD_URL: "http://localhost/upload/file/"
      
      # JVM配置
      JAVA_OPTS: "-Xms512m -Xmx1024m"
    volumes:
      - upload_data:/app/upload
      - logs_data:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - exam-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:8101/doc.html"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Vue.js前端
  exam-vue:
    build:
      context: ..
      dockerfile: docker/frontend/Dockerfile
    container_name: exam-vue
    restart: unless-stopped
    networks:
      - exam-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    build:
      context: ..
      dockerfile: docker/nginx/Dockerfile
    container_name: exam-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - upload_data:/app/upload:ro
    depends_on:
      exam-api:
        condition: service_healthy
      exam-vue:
        condition: service_healthy
    networks:
      - exam-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  exam-network:
    driver: bridge

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  upload_data:
    driver: local
  logs_data:
    driver: local
