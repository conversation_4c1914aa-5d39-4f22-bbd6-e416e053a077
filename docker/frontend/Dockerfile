# 多阶段构建：Stage 1 - Node.js构建
FROM node:22-alpine AS builder

WORKDIR /app

# 复制package文件并安装依赖（利用Docker缓存）
COPY exam-vue/package*.json ./
RUN npm ci --only=production

# 复制源代码并构建
COPY exam-vue/ .
RUN npm run build:prod

# Stage 2 - Nginx服务
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY docker/frontend/nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
