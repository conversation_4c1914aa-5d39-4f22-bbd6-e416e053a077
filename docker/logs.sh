#!/bin/bash

# 考试系统Docker日志查看脚本

# 进入docker目录
cd "$(dirname "$0")"

# 检查参数
if [ $# -eq 0 ]; then
    echo "📋 考试系统日志查看工具"
    echo ""
    echo "用法: $0 [服务名] [选项]"
    echo ""
    echo "服务名:"
    echo "  all       - 所有服务日志"
    echo "  mysql     - MySQL数据库日志"
    echo "  exam-api  - Spring Boot后端日志"
    echo "  exam-vue  - Vue.js前端日志"
    echo "  nginx     - Nginx反向代理日志"
    echo ""
    echo "选项:"
    echo "  -f        - 实时跟踪日志"
    echo "  --tail=N  - 显示最后N行"
    echo ""
    echo "示例:"
    echo "  $0 all -f                # 实时查看所有服务日志"
    echo "  $0 exam-api --tail=100   # 查看后端最后100行日志"
    echo "  $0 nginx -f              # 实时查看Nginx日志"
    exit 0
fi

SERVICE=$1
shift

case $SERVICE in
    "all")
        echo "📋 查看所有服务日志..."
        docker-compose logs "$@"
        ;;
    "mysql")
        echo "📋 查看MySQL日志..."
        docker-compose logs mysql "$@"
        ;;
    "exam-api")
        echo "📋 查看Spring Boot后端日志..."
        docker-compose logs exam-api "$@"
        ;;
    "exam-vue")
        echo "📋 查看Vue.js前端日志..."
        docker-compose logs exam-vue "$@"
        ;;
    "nginx")
        echo "📋 查看Nginx反向代理日志..."
        docker-compose logs nginx "$@"
        ;;
    *)
        echo "❌ 未知服务: $SERVICE"
        echo "使用 '$0' 查看帮助"
        exit 1
        ;;
esac
