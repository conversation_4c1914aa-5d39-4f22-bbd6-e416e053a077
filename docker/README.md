# 考试系统 Docker 部署指南

> 🐾 由 Claude 4.0 sonnet 精心打造的零代码修改容器化方案

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────┐
│                  Nginx (80)                     │
│            反向代理 + 静态文件服务                │
└─────────────┬───────────────┬───────────────────┘
              │               │
    ┌─────────▼─────────┐   ┌─▼──────────────┐
    │   Vue.js容器      │   │  Spring Boot   │
    │   (exam-vue)      │   │   (exam-api)   │
    │   Node.js 22      │   │    JDK 8       │
    └───────────────────┘   └─┬──────────────┘
                              │
                        ┌─────▼─────┐
                        │   MySQL   │
                        │    8.0    │
                        └───────────┘
```

## 🚀 快速开始

### 1. 构建镜像
```bash
cd docker
./build.sh
```

### 2. 启动服务
```bash
./start.sh
```

### 3. 访问系统
- **前端地址**: http://localhost
- **后端API**: http://localhost/exam/api
- **图片访问**: http://localhost/upload/file/
- **MySQL**: localhost:3306

## 📋 管理命令

| 命令 | 说明 |
|------|------|
| `./build.sh` | 构建所有Docker镜像 |
| `./start.sh` | 启动所有服务（自动检查目录） |
| `./stop.sh` | 停止所有服务 |
| `./logs.sh [服务名]` | 查看服务日志 |
| `./db-manage.sh [命令]` | 数据库管理工具 |

### 日志查看示例
```bash
# 查看所有服务日志
./logs.sh all -f

# 查看后端日志
./logs.sh exam-api --tail=100

# 查看Nginx日志
./logs.sh nginx -f
```

## 🔧 配置说明

### 环境变量覆盖（零代码修改）
系统通过环境变量覆盖原有配置，无需修改任何代码：

- `SPRING_DATASOURCE_URL`: 数据库连接地址
- `SPRING_DATASOURCE_USERNAME`: 数据库用户名
- `SPRING_DATASOURCE_PASSWORD`: 数据库密码
- `CONF_UPLOAD_URL`: 图片访问地址

### 数据持久化
- **MySQL数据**: `./mysql/data` 目录映射
- **MySQL初始化**: `./mysql/init` 目录映射
- **图片文件**: `upload_data` Volume
- **应用日志**: `logs_data` Volume

### 数据库管理
```bash
# 备份数据库
./db-manage.sh backup

# 恢复数据库
./db-manage.sh restore backup.sql

# 连接数据库
./db-manage.sh connect

# 查看状态
./db-manage.sh status

# 重置数据库（危险操作）
./db-manage.sh reset
```

### 网络配置
- **自定义网络**: `exam-network`
- **服务通信**: 通过容器名（如 mysql、exam-api）
- **外部访问**: 仅暴露Nginx 80端口

## 📁 目录结构

```
docker/
├── nginx/              # Nginx反向代理
│   ├── Dockerfile
│   └── nginx.conf
├── backend/            # Spring Boot后端
│   └── Dockerfile
├── frontend/           # Vue.js前端
│   ├── Dockerfile
│   └── nginx.conf
├── mysql/              # MySQL配置
├── docker-compose.yml  # 服务编排
├── .env               # 环境变量
├── build.sh           # 构建脚本
├── start.sh           # 启动脚本
├── stop.sh            # 停止脚本
├── logs.sh            # 日志脚本
└── README.md          # 使用文档
```

## 🎯 核心特性

✅ **零代码修改**: 保持所有现有功能不变  
✅ **图片功能完整**: 通过Nginx优化，性能更佳  
✅ **生产就绪**: 支持缓存、压缩、健康检查  
✅ **易于维护**: 标准化容器部署，便于扩展  

## 🔍 故障排除

### 服务状态检查
```bash
docker-compose ps
```

### 健康检查
```bash
# 检查所有服务健康状态
docker-compose ps --format "table {{.Name}}\t{{.Status}}"

# 检查特定服务
docker inspect exam-api --format='{{.State.Health.Status}}'
```

### 常见问题

1. **端口冲突**: 确保80和3306端口未被占用
2. **权限问题**: 确保脚本有执行权限 `chmod +x *.sh`
3. **内存不足**: 调整 `.env` 中的 `JAVA_OPTS` 参数

## 🛠️ 开发模式

如需开发调试，可以单独启动某个服务：
```bash
# 只启动数据库
docker-compose up -d mysql

# 只启动后端（用于前端开发）
docker-compose up -d mysql exam-api
```

## 📞 技术支持

如遇问题，请检查：
1. Docker和Docker Compose版本
2. 系统资源（内存、磁盘空间）
3. 网络连接
4. 服务日志 `./logs.sh all`

---

*🐾 Powered by Claude 4.0 sonnet - 让容器化部署变得简单优雅*
