# 多阶段构建：Stage 1 - Maven构建
FROM maven:3.8.6-openjdk-8-slim AS builder

WORKDIR /app

# 复制pom.xml并下载依赖（利用Docker缓存）
COPY exam-api/pom.xml .
RUN mvn dependency:go-offline -B

# 复制源代码并构建
COPY exam-api/src ./src
RUN mvn clean package -DskipTests -B

# Stage 2 - 运行环境
FROM openjdk:8-jre-alpine

WORKDIR /app

# 创建上传目录
RUN mkdir -p /app/upload

# 复制构建好的JAR文件
COPY --from=builder /app/target/exam-api.jar app.jar

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8101/exam/api/sys/config/detail || exit 1

# 暴露端口
EXPOSE 8101

# 启动应用
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
