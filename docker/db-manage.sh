#!/bin/bash

# MySQL数据库管理工具
# 支持备份、恢复、重置等操作

set -e

# 进入docker目录
cd "$(dirname "$0")"

# 数据库配置
DB_CONTAINER="exam-mysql"
DB_USER="root"
DB_PASSWORD="123456"
DB_NAME="exam"
BACKUP_DIR="./mysql/backups"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 显示帮助信息
show_help() {
    echo "📋 MySQL数据库管理工具"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  backup              - 备份数据库"
    echo "  restore <文件>      - 恢复数据库"
    echo "  reset               - 重置数据库（危险操作）"
    echo "  connect             - 连接到数据库"
    echo "  status              - 查看数据库状态"
    echo "  logs                - 查看数据库日志"
    echo ""
    echo "示例:"
    echo "  $0 backup                    # 备份数据库"
    echo "  $0 restore backup.sql       # 恢复数据库"
    echo "  $0 reset                     # 重置数据库"
    echo "  $0 connect                   # 连接数据库"
}

# 检查容器是否运行
check_container() {
    if ! docker ps | grep -q "$DB_CONTAINER"; then
        echo "❌ MySQL容器未运行，请先启动服务："
        echo "   ./start.sh"
        exit 1
    fi
}

# 备份数据库
backup_db() {
    check_container
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/exam_backup_$timestamp.sql"
    
    echo "📦 开始备份数据库..."
    docker exec "$DB_CONTAINER" mysqldump -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$backup_file"
    
    if [ $? -eq 0 ]; then
        echo "✅ 备份完成: $backup_file"
        echo "📊 备份文件大小: $(du -h "$backup_file" | cut -f1)"
    else
        echo "❌ 备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_db() {
    check_container
    
    local backup_file="$1"
    
    if [ -z "$backup_file" ]; then
        echo "❌ 请指定备份文件"
        echo "用法: $0 restore <备份文件>"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ 备份文件不存在: $backup_file"
        exit 1
    fi
    
    echo "⚠️  即将恢复数据库，这将覆盖现有数据！"
    read -p "确认继续？(y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "❌ 操作已取消"
        exit 0
    fi
    
    echo "📥 开始恢复数据库..."
    docker exec -i "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_file"
    
    if [ $? -eq 0 ]; then
        echo "✅ 恢复完成"
    else
        echo "❌ 恢复失败"
        exit 1
    fi
}

# 重置数据库
reset_db() {
    echo "⚠️  危险操作：即将重置数据库！"
    echo "这将删除所有数据并重新初始化数据库。"
    read -p "确认继续？(y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "❌ 操作已取消"
        exit 0
    fi
    
    echo "🛑 停止服务..."
    docker-compose down
    
    echo "🗑️  清理数据目录..."
    rm -rf mysql/data/*
    
    echo "🚀 重新启动服务..."
    ./start.sh
    
    echo "✅ 数据库重置完成"
}

# 连接数据库
connect_db() {
    check_container
    
    echo "🔗 连接到MySQL数据库..."
    echo "数据库: $DB_NAME"
    echo "用户: $DB_USER"
    echo ""
    docker exec -it "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
}

# 查看数据库状态
show_status() {
    check_container
    
    echo "📊 MySQL数据库状态："
    echo ""
    docker exec "$DB_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT 'Database Status' as Info;
        SHOW DATABASES;
        USE $DB_NAME;
        SHOW TABLES;
        SELECT COUNT(*) as 'Total Tables' FROM information_schema.tables WHERE table_schema = '$DB_NAME';
    "
}

# 查看数据库日志
show_logs() {
    echo "📋 MySQL数据库日志："
    docker-compose logs mysql
}

# 主程序
case "$1" in
    "backup")
        backup_db
        ;;
    "restore")
        restore_db "$2"
        ;;
    "reset")
        reset_db
        ;;
    "connect")
        connect_db
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    *)
        show_help
        ;;
esac
