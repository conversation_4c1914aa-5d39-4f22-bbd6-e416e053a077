# MySQL 数据库配置说明

## 📁 目录结构

```
mysql/
├── init/           # 初始化SQL脚本目录
│   └── 01-exam.sql # 考试系统初始化脚本
├── data/           # MySQL数据文件目录（自动创建）
└── README.md       # 本说明文件
```

## 🔧 初始化机制

### 自动初始化
- **init目录**：存放所有初始化SQL脚本
- **执行顺序**：按文件名字母顺序执行
- **命名规范**：建议使用 `01-xxx.sql`, `02-xxx.sql` 格式

### 数据持久化
- **data目录**：MySQL数据文件存储
- **自动创建**：启动脚本会自动检查并创建
- **数据安全**：容器删除后数据仍然保留

## 📋 使用说明

### 添加初始化脚本
1. 将SQL文件放入 `init/` 目录
2. 使用数字前缀控制执行顺序
3. 重新启动容器生效（仅首次初始化时）

### 数据备份
```bash
# 备份数据
docker exec exam-mysql mysqldump -uroot -p123456 exam > backup.sql

# 恢复数据
docker exec -i exam-mysql mysql -uroot -p123456 exam < backup.sql
```

### 重置数据库
```bash
# 停止服务
./stop.sh

# 清理数据（谨慎操作！）
rm -rf mysql/data/*

# 重新启动（会重新初始化）
./start.sh
```

## ⚠️ 注意事项

1. **首次启动**：会自动执行init目录中的所有SQL脚本
2. **数据持久化**：data目录映射到容器的 `/var/lib/mysql`
3. **权限问题**：确保data目录有正确的读写权限
4. **备份重要**：生产环境请定期备份数据
