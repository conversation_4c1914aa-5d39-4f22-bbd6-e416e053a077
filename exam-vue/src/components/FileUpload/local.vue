<template>
  <div class="content">
    <el-upload
      v-model="fileUrl"
      :action="server"
      :accept="accept"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-exceed="handleExceed"
      :on-error="handleError"
      :before-upload="beforeUpload"

      :drag="listType!=='picture'"
      :limit="limit"
      :headers="header"
      :file-list="fileList"
      :list-type="listType"
    >

      <el-button v-if="listType==='picture'" size="small" type="primary">点击上传</el-button>

      <i v-if="listType!=='picture'" class="el-icon-upload" />
      <div v-if="listType!=='picture'" class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <div v-if="tips" slot="tip" class="el-upload__tip">{{ tips }}</div>
    </el-upload>

  </div>

</template>

<script>

import { getToken } from '@/utils/auth'

export default {
  name: 'FileUploadLocal',
  props: {
    value: String,
    accept: String,
    tips: String,
    listType: String,
    limit: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      server: `${process.env.VUE_APP_BASE_API}/common/api/file/upload`,
      fileList: [],
      fileUrl: '',
      header: {},
      // 文件大小限制（50MB，与nginx配置保持一致）
      maxFileSize: 50 * 1024 * 1024 // 50MB in bytes
    }
  },

  watch: {
    // 检测查询变化
    value: {
      handler() {
        this.fillValue()
      }
    }
  },

  created() {
    this.fillValue()
    this.header = { token: getToken() }
  },

  methods: {

    fillValue() {
      this.fileList = []
      this.fileUrl = this.value
      if (this.fileUrl) {
        // 处理多个图片URL（用逗号分隔）
        const urls = this.fileUrl.split(',').map(url => url.trim()).filter(url => url)
        if (urls.length > 0) {
          // 如果有多个URL，只显示第一个（保持兼容性）
          const firstUrl = urls[0]
          this.fileList = [{ name: firstUrl, url: firstUrl }]

          // 如果有多个图片，在控制台提示
          if (urls.length > 1) {
            console.log('检测到多个图片URL，当前只显示第一个：', urls)
          }
        }
      }
    },

    // 文件超出个数限制时的钩子
    handleExceed() {
      this.$message.warning(`每次只能上传 ${this.limit} 个文件`)
    },
    // 删除文件之前的钩子
    beforeRemove() {
      return this.$confirm(`确定移除文件吗？`)
    },

    // 文件列表移除文件时的钩子
    handleRemove() {
      this.$emit('input', '')
      this.fileList = []
    },

    // 文件上传前的钩子（文件大小检查）
    beforeUpload(file) {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        const maxSizeMB = Math.round(this.maxFileSize / (1024 * 1024))
        this.$message({
          type: 'error',
          message: `文件大小不能超过 ${maxSizeMB}MB，当前文件大小为 ${(file.size / (1024 * 1024)).toFixed(2)}MB`
        })
        return false
      }
      return true
    },

    // 文件上传错误时的钩子
    handleError(error, file) {
      console.error('文件上传失败:', error)

      // 检查是否是413错误（文件过大）
      if (error.status === 413) {
        const maxSizeMB = Math.round(this.maxFileSize / (1024 * 1024))
        this.$message({
          type: 'error',
          message: `文件大小超过服务器限制（${maxSizeMB}MB），请选择更小的文件`
        })
      } else {
        this.$message({
          type: 'error',
          message: `文件上传失败: ${error.message || '网络错误'}`
        })
      }

      // 清空文件列表
      this.fileList = []
    },

    // 文件上传成功时的钩子
    handleSuccess(response) {
      if (response.code === 1) {
        this.$message({
          type: 'error',
          message: response.msg
        })

        this.fileList = []
        return
      }
      this.$emit('input', response.data.url)
    }

  }
}
</script>
