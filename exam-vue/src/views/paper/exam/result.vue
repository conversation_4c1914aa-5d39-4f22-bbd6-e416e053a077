<template>
  <div class="app-container">

    <h2 class="text-center">{{ paperData.title }}</h2>
    <p class="text-center" style="color: #666">{{ paperData.createTime }}</p>

    <!-- 考试基本信息 -->
    <el-row :gutter="24" style="margin-top: 30px">
      <el-col :span="12" class="text-center">
        <span style="color: #666; font-size: 16px;">考生姓名：</span>
        <span style="font-size: 16px; font-weight: bold;">{{ paperData.userId_dictText }}</span>
      </el-col>
      <el-col :span="12" class="text-center">
        <span style="color: #666; font-size: 16px;">考试用时：</span>
        <span style="font-size: 16px; font-weight: bold;">{{ paperData.userTime }}分钟</span>
      </el-col>
    </el-row>

    <!-- 醒目的成绩展示卡片 -->
    <el-card class="score-card" style="margin-top: 40px;">
      <div class="score-container">
        <div class="score-title">考试成绩</div>
        <div class="score-display">
          <span class="score-number" :class="isPass ? 'score-pass' : 'score-fail'">
            {{ paperData.userScore }}
          </span>
          <span class="score-total">/ {{ paperData.totalScore }}分</span>
        </div>
        <div class="score-status" :class="isPass ? 'status-pass' : 'status-fail'">
          <i :class="isPass ? 'el-icon-success' : 'el-icon-error'"></i>
          {{ isPass ? '恭喜通过！' : '未达及格线' }}
        </div>
        <div class="score-detail">
          <span style="color: #666;">及格分数：{{ paperData.qualifyScore }}分</span>
        </div>
      </div>
    </el-card>

  </div>
</template>

<script>

import { paperResult } from '@/api/paper/exam'

export default {
  data() {
    return {
      // 试卷ID
      paperId: '',
      paperData: {}
    }
  },
  computed: {
    // 计算是否及格
    isPass() {
      return this.paperData.userScore >= this.paperData.qualifyScore
    }
  },
  created() {
    const id = this.$route.params.id
    if (typeof id !== 'undefined') {
      this.paperId = id
      this.fetchData(id)
    }
  },
  methods: {
    fetchData(id) {
      const params = { id: id }
      paperResult(params).then(response => {
        // 试卷内容
        this.paperData = response.data
      })
    }
  }
}
</script>

<style scoped>

  /* 成绩展示卡片样式 */
  .score-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: none;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .score-container {
    text-align: center;
    padding: 20px;
  }

  .score-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
  }

  .score-display {
    margin-bottom: 20px;
  }

  .score-number {
    font-size: 72px;
    font-weight: bold;
    line-height: 1;
  }

  .score-pass {
    color: #67C23A;
  }

  .score-fail {
    color: #F56C6C;
  }

  .score-total {
    font-size: 24px;
    color: #666;
    margin-left: 10px;
  }

  .score-status {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .status-pass {
    color: #67C23A;
  }

  .status-fail {
    color: #F56C6C;
  }

  .score-detail {
    font-size: 16px;
    color: #666;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .score-number {
      font-size: 48px;
    }

    .score-title {
      font-size: 20px;
    }

    .score-status {
      font-size: 18px;
    }
  }

</style>

