<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scars.exam.modules.sys.user.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scars.exam.modules.sys.user.entity.SysUser">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="real_name" property="realName" />
        <result column="password" property="password" />
        <result column="salt" property="salt" />
        <result column="role_ids" property="roleIds" />
        <result column="depart_id" property="departId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`user_name`,`real_name`,`password`,`salt`,`role_ids`,`depart_id`,`create_time`,`update_time`,`state`
    </sql>

</mapper>
