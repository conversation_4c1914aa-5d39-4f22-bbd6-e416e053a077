package org.scars.exam.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.scars.exam.ability.upload.config.UploadConfig;

/**
 * Web MVC 配置
 * 添加静态资源映射，提供更优雅的图片访问方式
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private UploadConfig uploadConfig;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加静态资源映射
        // 将 /upload/file/** 映射到物理目录
        registry.addResourceHandler("/upload/file/**")
                .addResourceLocations("file:" + uploadConfig.getDir());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 添加CORS配置，允许前端访问图片资源
        registry.addMapping("/upload/file/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "HEAD", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false);
    }
}
