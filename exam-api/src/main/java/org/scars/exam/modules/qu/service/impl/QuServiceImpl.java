package org.scars.exam.modules.qu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.scars.exam.ability.upload.config.UploadConfig;
import org.scars.exam.ability.upload.dto.UploadReqDTO;
import org.scars.exam.ability.upload.dto.UploadRespDTO;
import org.scars.exam.ability.upload.service.UploadService;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.exception.ServiceException;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.core.utils.FileNameUtils;
import org.scars.exam.core.utils.ImageDownloadUtils;
import org.scars.exam.core.utils.ZipFileUtils;
import org.scars.exam.core.utils.excel.ExportExcel;
import org.scars.exam.core.utils.excel.ImportExcel;
import org.scars.exam.modules.qu.dto.QuAnswerDTO;
import org.scars.exam.modules.qu.dto.QuDTO;
import org.scars.exam.modules.qu.dto.export.QuExportDTO;
import org.scars.exam.modules.qu.dto.ext.QuDetailDTO;
import org.scars.exam.modules.qu.dto.request.QuQueryReqDTO;
import org.scars.exam.modules.qu.entity.Qu;
import org.scars.exam.modules.qu.entity.QuAnswer;
import org.scars.exam.modules.qu.entity.QuRepo;
import org.scars.exam.modules.qu.enums.QuType;
import org.scars.exam.modules.qu.mapper.QuMapper;
import org.scars.exam.modules.qu.service.QuAnswerService;
import org.scars.exam.modules.qu.service.QuRepoService;
import org.scars.exam.modules.qu.service.QuService;
import org.scars.exam.modules.qu.utils.ImageCheckUtils;
import org.scars.exam.modules.repo.service.RepoService;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 语言设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25 10:17
 */
@Slf4j
@Service
public class QuServiceImpl extends ServiceImpl<QuMapper, Qu> implements QuService {

    @Autowired
    private QuAnswerService quAnswerService;

    @Autowired
    private QuRepoService quRepoService;

    @Autowired
    private ImageCheckUtils imageCheckUtils;

    @Autowired
    private UploadService uploadService;

    @Override
    public IPage<QuDTO> paging(PagingReqDTO<QuQueryReqDTO> reqDTO) {

        //创建分页对象
        Page page = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());

        //转换结果
        IPage<QuDTO> pageData = baseMapper.paging(page, reqDTO.getParams());
        return pageData;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        // 移除题目
        this.removeByIds(ids);

        // 移除选项
        QueryWrapper<QuAnswer> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(QuAnswer::getQuId, ids);
        quAnswerService.remove(wrapper);

        // 移除题库绑定
        QueryWrapper<QuRepo> wrapper1 = new QueryWrapper<>();
        wrapper1.lambda().in(QuRepo::getQuId, ids);
        quRepoService.remove(wrapper1);
    }

    @Override
    public List<Qu> listByRandom(String repoId, Integer quType, List<String> excludes, Integer size) {
        return baseMapper.listByRandom(repoId, quType, excludes, size);
    }

    @Override
    public QuDetailDTO detail(String id) {

        QuDetailDTO respDTO = new QuDetailDTO();
        Qu qu = this.getById(id);
        BeanMapper.copy(qu, respDTO);

        List<QuAnswerDTO> answerList = quAnswerService.listByQu(id);
        respDTO.setAnswerList(answerList);

        List<String> repoIds = quRepoService.listByQu(id);
        respDTO.setRepoIds(repoIds);

        return respDTO;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(QuDetailDTO reqDTO) {


        // 校验数据
        this.checkData(reqDTO, "");

        Qu qu = new Qu();
        BeanMapper.copy(reqDTO, qu);

        // 校验图片地址
        imageCheckUtils.checkImage(qu.getImage(), "题干图片地址错误！");

        // 更新
        this.saveOrUpdate(qu);

        // 保存全部问题
        quAnswerService.saveAll(qu.getId(), reqDTO.getAnswerList());

        // 保存到题库
        quRepoService.saveAll(qu.getId(), qu.getQuType(), reqDTO.getRepoIds());

    }

    @Override
    public List<QuExportDTO> listForExport(QuQueryReqDTO query) {
        return baseMapper.listForExport(query);
    }

    @Override
    public int importExcel(List<QuExportDTO> dtoList) {

        //根据题目名称分组
        Map<Integer, List<QuExportDTO>> anMap = new HashMap<>(16);

        //题目本体信息
        Map<Integer, QuExportDTO> quMap = new HashMap<>(16);

        //数据分组
        for (QuExportDTO item : dtoList) {

            // 空白的ID
            if (StringUtils.isEmpty(item.getNo())) {
                continue;
            }

            Integer key;
            //序号
            try {
                key = Integer.parseInt(item.getNo());
            } catch (Exception e) {
                continue;
            }

            //如果已经有题目了，直接处理选项
            if (anMap.containsKey(key)) {
                anMap.get(key).add(item);
            } else {
                //如果没有，将题目内容和选项一起
                List<QuExportDTO> subList = new ArrayList<>();
                subList.add(item);
                anMap.put(key, subList);
                quMap.put(key, item);
            }
        }

        int count = 0;
        try {

            //循环题目插入
            for (Integer key : quMap.keySet()) {

                QuExportDTO im = quMap.get(key);

                //题目基本信息
                QuDetailDTO qu = new QuDetailDTO();
                qu.setContent(im.getQContent());
                qu.setAnalysis(im.getQAnalysis());
                qu.setQuType(Integer.parseInt(im.getQuType()));
                qu.setCreateTime(new Date());

                //设置回答列表
                List<QuAnswerDTO> answerList = this.processAnswerList(anMap.get(key));
                //设置题目
                qu.setAnswerList(answerList);
                //设置引用题库
                qu.setRepoIds(im.getRepoList());
                // 保存答案
                this.save(qu);
                count++;
            }

        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(1, "导入出现问题，行：" + count + "，" + e.getMessage());
        }

        return count;
    }

    /**
     * 处理回答列表
     *
     * @param importList
     * @return
     */
    private List<QuAnswerDTO> processAnswerList(List<QuExportDTO> importList) {

        List<QuAnswerDTO> list = new ArrayList<>(16);
        for (QuExportDTO item : importList) {
            QuAnswerDTO a = new QuAnswerDTO();
            a.setIsRight("1".equals(item.getAIsRight()));
            a.setContent(item.getAContent());
            a.setAnalysis(item.getAAnalysis());
            a.setId("");
            list.add(a);
        }
        return list;
    }

    /**
     * 校验题目信息
     *
     * @param qu
     * @param no
     * @throws Exception
     */
    public void checkData(QuDetailDTO qu, String no) {


        if (StringUtils.isEmpty(qu.getContent())) {
            throw new ServiceException(1, no + "题目内容不能为空！");
        }


        if (CollectionUtils.isEmpty(qu.getRepoIds())) {
            throw new ServiceException(1, no + "至少要选择一个题库！");
        }

        List<QuAnswerDTO> answers = qu.getAnswerList();


            if (CollectionUtils.isEmpty(answers)) {
                throw new ServiceException(1, no + "客观题至少要包含一个备选答案！");
            }


            int trueCount = 0;
            for (QuAnswerDTO a : answers) {

                if (a.getIsRight() == null) {
                    throw new ServiceException(1, no + "必须定义选项是否正确项！");
                }

                if (StringUtils.isEmpty(a.getContent())) {
                    throw new ServiceException(1, no + "选项内容不为空！");
                }

                if (a.getIsRight()) {
                    trueCount += 1;
                }
            }

            if (trueCount == 0) {
                throw new ServiceException(1, no + "至少要包含一个正确项！");
            }


            //单选题
            if (qu.getQuType().equals(QuType.RADIO) && trueCount > 1) {
                throw new ServiceException(1, no + "单选题不能包含多个正确项！");
            }

    }

    @Override
    public void exportQuestionZip(List<String> quIds, HttpServletResponse response) throws Exception {
        if (CollectionUtils.isEmpty(quIds)) {
            throw new ServiceException("请选择要导出的题目");
        }

        // 创建临时目录
        String tempDir = ZipFileUtils.createTempDirectory("qu_export_");
        String imagesDir = tempDir + File.separator + "images";
        new File(imagesDir).mkdirs();

        try {
            // 查询题目数据
            List<QuExportDTO> exportList = new ArrayList<>();
            int no = 1;

            for (String quId : quIds) {
                QuDetailDTO quDetail = this.detail(quId);
                if (quDetail != null) {
                    // 处理题目图片
                    List<String> qImageFiles = new ArrayList<>();
                    if (StringUtils.hasText(quDetail.getImage())) {
                        List<String> imageUrls = Arrays.asList(quDetail.getImage().split(","));
                        qImageFiles = downloadQuestionImages(imageUrls, quId, imagesDir);
                    }

                    // 处理答案
                    List<QuAnswerDTO> answers = quDetail.getAnswerList();
                    if (answers != null) {
                        for (int i = 0; i < answers.size(); i++) {
                            QuAnswerDTO answer = answers.get(i);
                            QuExportDTO exportDTO = new QuExportDTO();

                            if (i == 0) {
                                // 第一个答案包含题目信息
                                exportDTO.setNo(String.valueOf(no));
                                exportDTO.setQuType(String.valueOf(quDetail.getQuType()));
                                exportDTO.setQContent(quDetail.getContent());
                                exportDTO.setQAnalysis(quDetail.getAnalysis());
                                exportDTO.setQImage(quDetail.getImage());
                                exportDTO.setQImageFiles(FileNameUtils.fileNamesToString(qImageFiles));
                                exportDTO.setRepoList(quDetail.getRepoIds());
                            } else {
                                // 其他答案只包含答案信息
                                exportDTO.setNo(String.valueOf(no));
                                exportDTO.setQuType("0");
                                exportDTO.setQContent("");
                                exportDTO.setQAnalysis("");
                                exportDTO.setQImage("");
                                exportDTO.setQImageFiles("");
                                exportDTO.setRepoList(null);
                            }

                            // 处理答案图片
                            List<String> aImageFiles = new ArrayList<>();
                            if (StringUtils.hasText(answer.getImage())) {
                                List<String> answerImageUrls = Arrays.asList(answer.getImage().split(","));
                                aImageFiles = downloadAnswerImages(answerImageUrls, answer.getId(), imagesDir);
                            }

                            exportDTO.setAIsRight(answer.getIsRight() ? "1" : "0");
                            exportDTO.setAContent(answer.getContent());
                            exportDTO.setAAnalysis(answer.getAnalysis());
                            exportDTO.setAImage(answer.getImage());
                            exportDTO.setAImageFiles(FileNameUtils.fileNamesToString(aImageFiles));

                            exportList.add(exportDTO);
                        }
                    }
                    no++;
                }
            }

            // 生成Excel文件
            String excelFileName = "questions_" + System.currentTimeMillis() + ".xlsx";
            String excelFilePath = tempDir + File.separator + excelFileName;

            try (FileOutputStream fos = new FileOutputStream(excelFilePath)) {
                new ExportExcel("试题", QuExportDTO.class).setDataList(exportList).write(fos);
            }

            // 创建ZIP文件
            String zipFileName = "questions_export_" + System.currentTimeMillis() + ".zip";
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(zipFileName, "UTF-8"));

            ZipFileUtils.createZipWithExcelAndImages(
                new File(excelFilePath),
                new File(imagesDir),
                response.getOutputStream()
            );

        } finally {
            // 清理临时文件
            ZipFileUtils.deleteDirectory(tempDir);
        }
    }

    @Override
    public String importQuestionZip(MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        // 验证ZIP文件格式
        File tempZipFile = File.createTempFile("import_", ".zip");
        file.transferTo(tempZipFile);

        if (!ZipFileUtils.isZipFile(tempZipFile)) {
            tempZipFile.delete();
            throw new ServiceException("文件格式错误，请上传ZIP文件");
        }

        // 创建临时解压目录
        String tempDir = ZipFileUtils.createTempDirectory("qu_import_");

        int successCount = 0;
        int errorCount = 0;

        try {
            // 解压ZIP文件
            ZipFileUtils.extractZipFile(new FileInputStream(tempZipFile), tempDir);

            // 查找Excel文件
            String excelFileName = ZipFileUtils.findExcelFileInZip(tempZipFile);
            if (excelFileName == null) {
                throw new ServiceException("ZIP文件中未找到Excel文件");
            }

            String excelFilePath = tempDir + File.separator + excelFileName;
            File excelFile = new File(excelFilePath);
            if (!excelFile.exists()) {
                throw new ServiceException("Excel文件不存在");
            }

            // 读取Excel数据
            ImportExcel importExcel = new ImportExcel(excelFile.getName(), new FileInputStream(excelFile), 1, 0);
            List<QuExportDTO> importList = importExcel.getDataList(QuExportDTO.class);

            if (CollectionUtils.isEmpty(importList)) {
                throw new ServiceException("Excel文件中没有数据");
            }

            // 处理导入数据
            String imagesDir = tempDir + File.separator + "images";
            successCount = processImportData(importList, imagesDir);

        } catch (Exception e) {
            errorCount++;
            throw e;
        } finally {
            // 清理临时文件
            tempZipFile.delete();
            ZipFileUtils.deleteDirectory(tempDir);
        }

        return String.format("导入完成！成功：%d 个题目，失败：%d 个。图片文件已自动上传并关联到对应题目。", successCount, errorCount);
    }

    /**
     * 下载题目图片
     */
    private List<String> downloadQuestionImages(List<String> imageUrls, String quId, String imagesDir) {
        List<String> fileNames = new ArrayList<>();
        for (int i = 0; i < imageUrls.size(); i++) {
            String url = imageUrls.get(i).trim();
            if (StringUtils.hasText(url)) {
                String extension = ImageDownloadUtils.getFileExtensionFromUrl(url);
                String fileName = FileNameUtils.generateQuestionImageName(quId, i + 1, extension);
                String filePath = imagesDir + File.separator + fileName;

                if (ImageDownloadUtils.downloadImageFromUrl(url, filePath)) {
                    fileNames.add(fileName);
                }
            }
        }
        return fileNames;
    }

    /**
     * 下载答案图片
     */
    private List<String> downloadAnswerImages(List<String> imageUrls, String answerId, String imagesDir) {
        List<String> fileNames = new ArrayList<>();
        for (int i = 0; i < imageUrls.size(); i++) {
            String url = imageUrls.get(i).trim();
            if (StringUtils.hasText(url)) {
                String extension = ImageDownloadUtils.getFileExtensionFromUrl(url);
                String fileName = FileNameUtils.generateAnswerImageName(answerId, i + 1, extension);
                String filePath = imagesDir + File.separator + fileName;

                if (ImageDownloadUtils.downloadImageFromUrl(url, filePath)) {
                    fileNames.add(fileName);
                }
            }
        }
        return fileNames;
    }

    /**
     * 处理导入数据
     */
    @Transactional(rollbackFor = Exception.class)
    private int processImportData(List<QuExportDTO> importList, String imagesDir) throws Exception {
        // 先导入基本数据
        int count = this.importExcel(importList);

        // 然后处理图片上传和关联
        try {
            processImageUpload(importList, imagesDir);
            log.info("图片处理完成，共处理 {} 道题目", count);
        } catch (Exception e) {
            log.error("处理图片上传失败", e);
            // 图片处理失败不影响题目导入，但记录错误
        }

        return count;
    }

    /**
     * 处理图片上传和关联
     */
    private void processImageUpload(List<QuExportDTO> importList, String imagesDir) {
        // 按题目序号分组
        Map<String, List<QuExportDTO>> quMap = new HashMap<>();

        for (QuExportDTO item : importList) {
            String no = item.getNo();
            if (StringUtils.hasText(no)) {
                if (!quMap.containsKey(no)) {
                    quMap.put(no, new ArrayList<>());
                }
                quMap.get(no).add(item);
            }
        }

        for (String key : quMap.keySet()) {
            try {
                List<QuExportDTO> questionItems = quMap.get(key);
                QuExportDTO mainItem = questionItems.get(0);

                // 查找对应的题目（通过题目内容匹配）
                String questionContent = mainItem.getQContent();
                if (StringUtils.hasText(questionContent)) {
                    log.info("处理题目图片：" + questionContent.substring(0, Math.min(20, questionContent.length())));

                    // 查找数据库中对应的题目
                    Qu question = findQuestionByContent(questionContent);
                    if (question != null) {
                        // 处理题目图片
                        if (StringUtils.hasText(mainItem.getQImageFiles())) {
                            List<String> imageFiles = FileNameUtils.stringToFileNames(mainItem.getQImageFiles());
                            List<String> uploadedUrls = uploadImages(imageFiles, imagesDir);
                            if (!uploadedUrls.isEmpty()) {
                                // 更新题目图片URL
                                question.setImage(String.join(",", uploadedUrls));
                                this.updateById(question);
                                log.info("题目图片关联成功：" + uploadedUrls.toString());
                            }
                        }

                        // 处理答案图片 - 使用内容匹配而不是顺序匹配
                        QueryWrapper<QuAnswer> answerWrapper = new QueryWrapper<>();
                        answerWrapper.eq("qu_id", question.getId());
                        answerWrapper.orderByAsc("id"); // 使用id排序，因为没有sort字段
                        List<QuAnswer> answers = quAnswerService.list(answerWrapper);

                        log.info("题目ID: " + question.getId() + ", 找到答案数量: " + answers.size() + ", 导入数据项数量: " + questionItems.size());

                        // 为每个导入的答案项查找对应的数据库答案
                        for (QuExportDTO answerItem : questionItems) {
                            if (StringUtils.hasText(answerItem.getAContent())) {
                                log.info("处理答案: " + answerItem.getAContent() + ", 图片文件: " + answerItem.getAImageFiles());

                                // 通过内容匹配找到对应的答案
                                QuAnswer matchedAnswer = null;
                                for (QuAnswer answer : answers) {
                                    if (answerItem.getAContent().trim().equals(answer.getContent().trim())) {
                                        matchedAnswer = answer;
                                        break;
                                    }
                                }

                                if (matchedAnswer != null) {
                                    log.info("找到匹配的答案: " + matchedAnswer.getContent());

                                    if (StringUtils.hasText(answerItem.getAImageFiles())) {
                                        List<String> answerImageFiles = FileNameUtils.stringToFileNames(answerItem.getAImageFiles());
                                        log.info("解析到的答案图片文件: " + answerImageFiles.toString());

                                        List<String> answerUploadedUrls = uploadImages(answerImageFiles, imagesDir);
                                        if (!answerUploadedUrls.isEmpty()) {
                                            // 更新答案图片URL
                                            matchedAnswer.setImage(String.join(",", answerUploadedUrls));
                                            quAnswerService.updateById(matchedAnswer);
                                            log.info("答案图片关联成功：" + answerUploadedUrls.toString());
                                        } else {
                                            log.warn("答案图片上传失败，文件列表: " + answerImageFiles.toString());
                                        }
                                    } else {
                                        log.info("答案没有图片文件");
                                    }
                                } else {
                                    log.warn("未找到匹配的答案: " + answerItem.getAContent());
                                }
                            }
                        }
                    } else {
                        log.warn("未找到对应的题目，可能是题目内容不匹配：" + questionContent.substring(0, Math.min(20, questionContent.length())));
                    }
                }

            } catch (Exception e) {
                log.error("处理第" + key + "题的图片失败", e);
            }
        }
    }

    /**
     * 根据题目内容查找题目
     */
    private Qu findQuestionByContent(String content) {
        if (StringUtils.hasText(content)) {
            QueryWrapper<Qu> wrapper = new QueryWrapper<>();
            wrapper.eq("content", content);
            wrapper.orderByDesc("create_time");
            wrapper.last("LIMIT 1");
            return this.getOne(wrapper);
        }
        return null;
    }

    /**
     * 上传图片文件
     */
    private List<String> uploadImages(List<String> imageFiles, String imagesDir) {
        List<String> urls = new ArrayList<>();

        log.info("开始上传图片，文件列表: " + imageFiles.toString() + ", 图片目录: " + imagesDir);

        for (String fileName : imageFiles) {
            try {
                String sourceFilePath = imagesDir + File.separator + fileName;
                File sourceFile = new File(sourceFilePath);

                log.info("检查图片文件: " + sourceFilePath + ", 存在: " + sourceFile.exists());

                if (sourceFile.exists()) {
                    // 简化实现：直接复制文件到上传目录
                    String uploadedUrl = copyImageToUploadDir(sourceFile, fileName);
                    if (uploadedUrl != null) {
                        urls.add(uploadedUrl);
                        log.info("图片上传成功: " + fileName + " -> " + uploadedUrl);
                    } else {
                        log.error("图片复制失败: " + fileName);
                    }
                } else {
                    log.error("图片文件不存在: " + sourceFilePath);
                }
            } catch (Exception e) {
                log.error("上传图片失败: " + fileName, e);
            }
        }

        log.info("图片上传完成，成功上传: " + urls.size() + " 个文件");
        return urls;
    }

    /**
     * 复制图片到上传目录
     */
    private String copyImageToUploadDir(File sourceFile, String fileName) {
        try {
            // 生成新的文件名（避免冲突）
            String newFileName = System.currentTimeMillis() + "_" + fileName;

            // 上传目录（从配置中获取，这里简化为固定路径）
            String uploadDir = "./upload/";
            File uploadDirFile = new File(uploadDir);
            if (!uploadDirFile.exists()) {
                uploadDirFile.mkdirs();
            }

            // 目标文件路径
            String targetFilePath = uploadDir + newFileName;
            File targetFile = new File(targetFilePath);

            // 复制文件
            try (FileInputStream fis = new FileInputStream(sourceFile);
                 FileOutputStream fos = new FileOutputStream(targetFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    fos.write(buffer, 0, length);
                }
            }

            // 返回访问URL
            return "http://localhost:8101/upload/file/" + newFileName;

        } catch (Exception e) {
            log.error("复制图片文件失败", e);
            return null;
        }
    }
}
