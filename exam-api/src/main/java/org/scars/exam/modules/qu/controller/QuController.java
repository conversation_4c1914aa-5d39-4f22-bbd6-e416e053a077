package org.scars.exam.modules.qu.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.multipart.MultipartFile;
import org.scars.exam.core.api.ApiRest;
import org.scars.exam.core.api.controller.BaseController;
import org.scars.exam.core.api.dto.BaseIdReqDTO;
import org.scars.exam.core.api.dto.BaseIdRespDTO;
import org.scars.exam.core.api.dto.BaseIdsReqDTO;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.core.utils.excel.ExportExcel;
import org.scars.exam.modules.qu.dto.QuDTO;
import org.scars.exam.modules.qu.dto.export.QuExportDTO;
import org.scars.exam.modules.qu.dto.ext.QuDetailDTO;
import org.scars.exam.modules.qu.dto.request.QuQueryReqDTO;
import org.scars.exam.modules.qu.entity.Qu;
import org.scars.exam.modules.qu.service.QuImageService;
import org.scars.exam.modules.qu.service.QuService;

import javax.servlet.http.HttpServletResponse;


import java.util.List;

/**
* <p>
* 问题题目控制器
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:25
*/
@Slf4j
@Api(tags={"问题题目"})
@RestController
@RequestMapping("/exam/api/qu/qu")
public class QuController extends BaseController {

    @Autowired
    private QuService baseService;

    @Autowired
    private QuImageService quImageService;

    /**
     * 添加或修改
     *
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "添加或修改")
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    public ApiRest<BaseIdRespDTO> save(@RequestBody QuDetailDTO reqDTO) {
        baseService.save(reqDTO);
        return super.success();
    }

    /**
     * 批量删除
     *
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public ApiRest edit(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.delete(reqDTO.getIds());
        return super.success();
    }

    /**
     * 查找详情
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "查找详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public ApiRest<QuDetailDTO> detail(@RequestBody BaseIdReqDTO reqDTO) {
        QuDetailDTO dto = baseService.detail(reqDTO.getId());
        return super.success(dto);
    }

    /**
     * 分页查找
     *
     * @param reqDTO
     * @return
     */
    @RequiresRoles("sa")
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = {RequestMethod.POST})
    public ApiRest<IPage<QuDTO>> paging(@RequestBody PagingReqDTO<QuQueryReqDTO> reqDTO) {

        //分页查询并转换
        IPage<QuDTO> page = baseService.paging(reqDTO);

        return super.success(page);
    }


    /**
     * 导出excel文件（旧版本，保留兼容性）
     */
    @RequiresRoles("sa")
    @ResponseBody
    @RequestMapping(value = "/export")
    public ApiRest exportFile(HttpServletResponse response, @RequestBody QuQueryReqDTO reqDTO) {


        // 导出文件名
        String fileName = "导出的试题-" + System.currentTimeMillis() + ".xlsx";

        try {

            int no = 0;
            String quId = "";
            List<QuExportDTO> list = baseService.listForExport(reqDTO);
            for (QuExportDTO item : list) {
                if (!quId.equals(item.getQId())) {
                    quId = item.getQId();
                    no += 1;
                } else {
                    item.setQuType("0");
                    item.setQContent("");
                    item.setQAnalysis("");
                    item.setRepoList(null);
                    item.setQImage("");
                    item.setQVideo("");
                }
                item.setNo(String.valueOf(no));
            }
            new ExportExcel("试题", QuExportDTO.class).setDataList(list).write(response, fileName).dispose();
            return super.success();
        } catch (Exception e) {
            return failure(e.getMessage());
        }
    }

    /**
     * 导出题目ZIP包（包含Excel和图片）
     *
     * @param quIds 题目ID列表
     * @param response
     * @return
     */
    @RequiresRoles("sa")
    @ResponseBody
    @RequestMapping(value = "/export-zip", method = RequestMethod.POST)
    public ApiRest exportQuestionZip(@RequestParam("quIds") List<String> quIds, HttpServletResponse response) {
        try {
            log.info("收到导出ZIP请求，题目数量: {}", quIds.size());
            baseService.exportQuestionZip(quIds, response);
            return super.success();
        } catch (Exception e) {
            log.error("导出题目ZIP包失败", e);
            return failure("导出失败：" + e.getMessage());
        }
    }

    /**
     * 测试ZIP导出接口是否可用
     */
    @ResponseBody
    @RequestMapping(value = "/test-zip")
    public ApiRest testZipExport() {
        return super.success("ZIP导出接口可用");
    }



    /**
     * 导入题目ZIP包（包含Excel和图片）
     *
     * @param file ZIP文件
     * @return
     */
    @RequiresRoles("sa")
    @ResponseBody
    @RequestMapping(value = "/import-zip", method = RequestMethod.POST)
    public ApiRest importQuestionZip(@RequestParam("file") MultipartFile file) {
        try {
            String result = baseService.importQuestionZip(file);
            return super.success(result);
        } catch (Exception e) {
            log.error("导入题目ZIP包失败", e);
            return super.failure("导入失败：" + e.getMessage());
        }
    }





    /**
     * 导出题目图片
     */
    @RequiresRoles("sa")
    @ResponseBody
    @RequestMapping(value = "/export-images")
    @ApiOperation(value = "导出题目图片", notes = "将所有题目的图片打包下载")
    public void exportImages(HttpServletResponse response) {
        try {
            quImageService.exportImages(response);
        } catch (Exception e) {
            log.error("导出题目图片失败", e);
        }
    }

    /**
     * 导入题目图片
     */
    @RequiresRoles("sa")
    @ResponseBody
    @RequestMapping(value = "/import-images")
    @ApiOperation(value = "导入题目图片", notes = "从ZIP文件导入题目图片")
    public ApiRest importImages(@RequestParam("file") MultipartFile file) {
        try {
            String result = quImageService.importImages(file);
            return super.success(result);
        } catch (Exception e) {
            log.error("导入题目图片失败", e);
            return super.failure("导入失败：" + e.getMessage());
        }
    }
}
