package org.scars.exam.modules.qu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import org.springframework.web.multipart.MultipartFile;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.modules.qu.dto.QuDTO;
import org.scars.exam.modules.qu.dto.export.QuExportDTO;
import org.scars.exam.modules.qu.dto.ext.QuDetailDTO;
import org.scars.exam.modules.qu.dto.request.QuQueryReqDTO;
import org.scars.exam.modules.qu.entity.Qu;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <p>
* 问题题目业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
public interface QuService extends IService<Qu> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<QuDTO> paging(PagingReqDTO<QuQueryReqDTO> reqDTO);

    /**
     * 删除试题
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 随机抽取题库的数据
     * @param repoId
     * @param quType
     * @param excludes 要排除的ID列表
     * @param size
     * @return
     */
    List<Qu> listByRandom(String repoId,
                          Integer quType,
                          List<String> excludes,
                          Integer size);

    /**
     * 问题详情
     * @param id
     * @return
     */
    QuDetailDTO detail(String id);

    /**
     * 保存试题
     * @param reqDTO
     */
    void save(QuDetailDTO reqDTO);

    /**
     * 查找导出列表
     * @param query
     * @return
     */
    List<QuExportDTO> listForExport(QuQueryReqDTO query);

    /**
     * 导入Excel
     * @param dtoList
     * @return
     */
    int importExcel(List<QuExportDTO> dtoList);

    /**
     * 导出题目ZIP包（包含Excel和图片）
     * @param quIds 题目ID列表
     * @param response HTTP响应
     * @throws Exception 异常
     */
    void exportQuestionZip(List<String> quIds, HttpServletResponse response) throws Exception;

    /**
     * 导入题目ZIP包（包含Excel和图片）
     * @param file ZIP文件
     * @return 导入结果信息
     * @throws Exception 异常
     */
    String importQuestionZip(MultipartFile file) throws Exception;
}
