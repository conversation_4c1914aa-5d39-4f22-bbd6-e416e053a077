package org.scars.exam.modules.paper.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.scars.exam.core.api.ApiRest;
import org.scars.exam.core.api.controller.BaseController;
import org.scars.exam.core.api.dto.BaseIdReqDTO;
import org.scars.exam.core.api.dto.BaseIdRespDTO;
import org.scars.exam.core.api.dto.BaseIdsReqDTO;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.modules.paper.dto.PaperDTO;
import org.scars.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import org.scars.exam.modules.paper.dto.request.PaperAnswerDTO;
import org.scars.exam.modules.paper.dto.request.PaperCreateReqDTO;
import org.scars.exam.modules.paper.dto.request.PaperListReqDTO;
import org.scars.exam.modules.paper.dto.request.PaperQuQueryDTO;
import org.scars.exam.modules.paper.dto.response.ExamDetailRespDTO;
import org.scars.exam.modules.paper.dto.response.ExamResultRespDTO;
import org.scars.exam.modules.paper.dto.response.PaperListRespDTO;
import org.scars.exam.modules.paper.entity.Paper;
import org.scars.exam.modules.paper.service.PaperService;
import org.scars.exam.modules.user.UserUtils;

/**
* <p>
* 试卷控制器
* </p>
*
* <AUTHOR>
* @since 2020-05-25 16:33
*/
@Api(tags={"试卷"})
@RestController
@RequestMapping("/exam/api/paper/paper")
public class PaperController extends BaseController {

    @Autowired
    private PaperService baseService;

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public ApiRest<IPage<PaperListRespDTO>> paging(@RequestBody PagingReqDTO<PaperListReqDTO> reqDTO) {
        //分页查询并转换
        IPage<PaperListRespDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }



    /**
     * 创建试卷
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "创建试卷")
    @RequestMapping(value = "/create-paper", method = { RequestMethod.POST})
    public ApiRest<BaseIdRespDTO> save(@RequestBody PaperCreateReqDTO reqDTO) {
        //复制参数
        String paperId = baseService.createPaper(UserUtils.getUserId(), reqDTO.getExamId());
        return super.success(new BaseIdRespDTO(paperId));
    }

    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试卷详情")
    @RequestMapping(value = "/paper-detail", method = { RequestMethod.POST})
    public ApiRest<ExamDetailRespDTO> paperDetail(@RequestBody BaseIdReqDTO reqDTO) {
        //根据ID删除
        ExamDetailRespDTO respDTO = baseService.paperDetail(reqDTO.getId());
        return super.success(respDTO);
    }

    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试题详情")
    @RequestMapping(value = "/qu-detail", method = { RequestMethod.POST})
    public ApiRest<PaperQuDetailDTO> quDetail(@RequestBody PaperQuQueryDTO reqDTO) {
        //根据ID删除
        PaperQuDetailDTO respDTO = baseService.findQuDetail(reqDTO.getPaperId(), reqDTO.getQuId());
        return super.success(respDTO);
    }

    /**
     * 填充答案
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "填充答案")
    @RequestMapping(value = "/fill-answer", method = { RequestMethod.POST})
    public ApiRest<PaperQuDetailDTO> fillAnswer(@RequestBody PaperAnswerDTO reqDTO) {
        //根据ID删除
        baseService.fillAnswer(reqDTO);
        return super.success();
    }


    /**
     * 交卷操作
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "交卷操作")
    @RequestMapping(value = "/hand-exam", method = { RequestMethod.POST})
    public ApiRest<PaperQuDetailDTO> handleExam(@RequestBody BaseIdReqDTO reqDTO) {
        //根据ID删除
        baseService.handExam(reqDTO.getId());
        return super.success();
    }


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "试卷详情")
    @RequestMapping(value = "/paper-result", method = { RequestMethod.POST})
    public ApiRest<ExamResultRespDTO> paperResult(@RequestBody BaseIdReqDTO reqDTO) {
        //根据ID删除
        ExamResultRespDTO respDTO = baseService.paperResult(reqDTO.getId());
        return super.success(respDTO);
    }


    /**
     * 检测用户有没有中断的考试
     * @return
     */
    @ApiOperation(value = "检测进行中的考试")
    @RequestMapping(value = "/check-process", method = { RequestMethod.POST})
    public ApiRest<PaperDTO> checkProcess() {
        //复制参数
        PaperDTO dto = baseService.checkProcess(UserUtils.getUserId());
        return super.success(dto);
    }
}
