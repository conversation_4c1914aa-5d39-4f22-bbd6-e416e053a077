package org.scars.exam.modules.repo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;
import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.core.utils.BeanMapper;
import org.scars.exam.modules.repo.dto.RepoDTO;
import org.scars.exam.modules.repo.dto.request.RepoReqDTO;
import org.scars.exam.modules.repo.dto.response.RepoRespDTO;
import org.scars.exam.modules.repo.entity.Repo;
import org.scars.exam.modules.repo.mapper.RepoMapper;
import org.scars.exam.modules.repo.service.RepoService;

/**
* <p>
* 语言设置 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
@Service
public class RepoServiceImpl extends ServiceImpl<RepoMapper, Repo> implements RepoService {

    @Override
    public IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
     }

    @Override
    public void save(RepoDTO reqDTO) {

        //复制参数
        Repo entity = new Repo();
        BeanMapper.copy(reqDTO, entity);
        this.saveOrUpdate(entity);
    }
}
