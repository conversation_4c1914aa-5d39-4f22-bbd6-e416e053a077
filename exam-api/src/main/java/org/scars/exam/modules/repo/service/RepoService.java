package org.scars.exam.modules.repo.service;

import org.scars.exam.core.api.dto.PagingReqDTO;
import org.scars.exam.modules.repo.dto.RepoDTO;
import org.scars.exam.modules.repo.dto.request.RepoReqDTO;
import org.scars.exam.modules.repo.dto.response.RepoRespDTO;
import org.scars.exam.modules.repo.entity.Repo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <p>
* 题库业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
public interface RepoService extends IService<Repo> {

    /**
    * 分页查询数据
    * @param reqDTO
    * @return
    */
    IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO);


    /**
     * 保存
     * @param reqDTO
     */
    void save(RepoDTO reqDTO);
}
