package org.scars.exam.modules.sys.user.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户重置密码请求DTO
 *
 * <AUTHOR> 4.0 sonnet
 * @since 2025-01-28
 */
@Data
@ApiModel(value = "用户重置密码请求DTO", description = "用户重置密码请求DTO")
public class SysUserResetPasswordReqDTO {

    @ApiModelProperty(value = "用户ID", required = true)
    @NotBlank(message = "用户ID不能为空")
    private String id;

    @ApiModelProperty(value = "新密码", required = true)
    @NotBlank(message = "新密码不能为空")
    private String password;
}
