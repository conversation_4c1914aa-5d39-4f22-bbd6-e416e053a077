package org.scars.exam.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * ZIP文件操作工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
public class ZipFileUtils {

    /**
     * 创建包含Excel和图片的ZIP文件
     * 
     * @param excelFile Excel文件
     * @param imagesDir 图片目录
     * @param outputStream 输出流
     * @throws IOException IO异常
     */
    public static void createZipWithExcelAndImages(File excelFile, File imagesDir, OutputStream outputStream) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
            
            // 添加Excel文件到ZIP
            addFileToZip(zos, excelFile, excelFile.getName());
            
            // 添加图片目录到ZIP
            if (imagesDir != null && imagesDir.exists() && imagesDir.isDirectory()) {
                addDirectoryToZip(zos, imagesDir, "images/");
            }
            
            zos.finish();
        }
    }

    /**
     * 解压ZIP文件到指定目录
     * 
     * @param zipInputStream ZIP输入流
     * @param destDir 目标目录
     * @return 解压的文件列表
     * @throws IOException IO异常
     */
    public static void extractZipFile(InputStream zipInputStream, String destDir) throws IOException {
        File destDirectory = new File(destDir);
        if (!destDirectory.exists()) {
            destDirectory.mkdirs();
        }

        try (ZipInputStream zis = new ZipInputStream(zipInputStream)) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File destFile = new File(destDirectory, entry.getName());
                
                // 安全检查：防止目录遍历攻击
                if (!destFile.getCanonicalPath().startsWith(destDirectory.getCanonicalPath())) {
                    throw new IOException("Entry is outside of the target dir: " + entry.getName());
                }
                
                if (entry.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    File parent = destFile.getParentFile();
                    if (!parent.exists()) {
                        parent.mkdirs();
                    }
                    
                    // 写入文件
                    try (FileOutputStream fos = new FileOutputStream(destFile)) {
                        IOUtils.copy(zis, fos);
                    }
                }
                zis.closeEntry();
            }
        }
    }

    /**
     * 向ZIP文件添加单个文件
     * 
     * @param zos ZIP输出流
     * @param file 要添加的文件
     * @param entryName ZIP中的条目名称
     * @throws IOException IO异常
     */
    public static void addFileToZip(ZipOutputStream zos, File file, String entryName) throws IOException {
        if (!file.exists() || !file.isFile()) {
            log.warn("文件不存在或不是文件: {}", file.getAbsolutePath());
            return;
        }

        ZipEntry entry = new ZipEntry(entryName);
        zos.putNextEntry(entry);

        try (FileInputStream fis = new FileInputStream(file)) {
            IOUtils.copy(fis, zos);
        }

        zos.closeEntry();
    }

    /**
     * 向ZIP文件添加目录
     * 
     * @param zos ZIP输出流
     * @param dir 要添加的目录
     * @param basePath ZIP中的基础路径
     * @throws IOException IO异常
     */
    public static void addDirectoryToZip(ZipOutputStream zos, File dir, String basePath) throws IOException {
        if (!dir.exists() || !dir.isDirectory()) {
            log.warn("目录不存在或不是目录: {}", dir.getAbsolutePath());
            return;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            String entryName = basePath + file.getName();
            
            if (file.isDirectory()) {
                // 递归添加子目录
                addDirectoryToZip(zos, file, entryName + "/");
            } else {
                // 添加文件
                addFileToZip(zos, file, entryName);
            }
        }
    }

    /**
     * 创建临时目录
     * 
     * @param prefix 目录前缀
     * @return 临时目录路径
     * @throws IOException IO异常
     */
    public static String createTempDirectory(String prefix) throws IOException {
        Path tempDir = Files.createTempDirectory(prefix);
        return tempDir.toString();
    }

    /**
     * 删除目录及其所有内容
     * 
     * @param dirPath 目录路径
     */
    public static void deleteDirectory(String dirPath) {
        try {
            File dir = new File(dirPath);
            if (dir.exists()) {
                deleteDirectoryRecursively(dir);
            }
        } catch (Exception e) {
            log.error("删除目录失败: {}", dirPath, e);
        }
    }

    /**
     * 递归删除目录
     * 
     * @param dir 目录
     */
    private static void deleteDirectoryRecursively(File dir) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectoryRecursively(file);
                } else {
                    file.delete();
                }
            }
        }
        dir.delete();
    }

    /**
     * 检查文件是否为ZIP格式
     * 
     * @param file 文件
     * @return 是否为ZIP格式
     */
    public static boolean isZipFile(File file) {
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] signature = new byte[4];
            if (fis.read(signature) == 4) {
                // ZIP文件的魔数：50 4B 03 04
                return signature[0] == 0x50 && signature[1] == 0x4B && 
                       signature[2] == 0x03 && signature[3] == 0x04;
            }
        } catch (IOException e) {
            log.error("检查ZIP文件格式失败", e);
        }
        
        return false;
    }

    /**
     * 获取ZIP文件中的Excel文件路径
     * 
     * @param zipFile ZIP文件
     * @return Excel文件路径，如果不存在返回null
     * @throws IOException IO异常
     */
    public static String findExcelFileInZip(File zipFile) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                String fileName = entry.getName().toLowerCase();
                if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                    return entry.getName();
                }
                zis.closeEntry();
            }
        }
        return null;
    }
}
