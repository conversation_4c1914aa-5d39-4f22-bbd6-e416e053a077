package org.scars.exam.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 图片下载工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
public class ImageDownloadUtils {

    private static final int CONNECT_TIMEOUT = 10000; // 10秒连接超时
    private static final int READ_TIMEOUT = 30000; // 30秒读取超时
    private static final String[] SUPPORTED_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp"};

    /**
     * 从URL下载图片到本地文件
     * 
     * @param imageUrl 图片URL
     * @param localFilePath 本地文件路径
     * @return 是否下载成功
     */
    public static boolean downloadImageFromUrl(String imageUrl, String localFilePath) {
        if (StringUtils.isBlank(imageUrl) || StringUtils.isBlank(localFilePath)) {
            log.warn("图片URL或本地路径为空");
            return false;
        }

        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 确保目录存在
                File localFile = new File(localFilePath);
                File parentDir = localFile.getParentFile();
                if (!parentDir.exists()) {
                    parentDir.mkdirs();
                }

                // 下载文件
                try (InputStream inputStream = connection.getInputStream();
                     FileOutputStream outputStream = new FileOutputStream(localFile)) {
                    IOUtils.copy(inputStream, outputStream);
                }

                log.debug("图片下载成功: {} -> {}", imageUrl, localFilePath);
                return true;
            } else {
                log.warn("图片下载失败，HTTP状态码: {}, URL: {}", responseCode, imageUrl);
                return false;
            }
        } catch (Exception e) {
            log.error("下载图片异常: {}", imageUrl, e);
            return false;
        }
    }

    /**
     * 批量下载图片
     * 
     * @param imageUrls 图片URL列表
     * @param targetDir 目标目录
     * @param fileNamePrefix 文件名前缀
     * @return 下载成功的文件名列表
     */
    public static List<String> batchDownloadImages(List<String> imageUrls, String targetDir, String fileNamePrefix) {
        List<String> downloadedFiles = new ArrayList<>();
        
        if (imageUrls == null || imageUrls.isEmpty()) {
            return downloadedFiles;
        }

        // 确保目标目录存在
        File dir = new File(targetDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            if (StringUtils.isBlank(imageUrl)) {
                continue;
            }

            // 生成本地文件名
            String extension = getFileExtensionFromUrl(imageUrl);
            if (StringUtils.isBlank(extension)) {
                extension = "jpg"; // 默认扩展名
            }
            
            String fileName = fileNamePrefix + "_" + (i + 1) + "." + extension;
            String localFilePath = targetDir + File.separator + fileName;

            // 下载图片
            if (downloadImageFromUrl(imageUrl, localFilePath)) {
                downloadedFiles.add(fileName);
            }
        }

        return downloadedFiles;
    }

    /**
     * 从URL中提取文件扩展名
     * 
     * @param url 图片URL
     * @return 文件扩展名
     */
    public static String getFileExtensionFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        // 移除查询参数
        int queryIndex = url.indexOf('?');
        if (queryIndex > 0) {
            url = url.substring(0, queryIndex);
        }

        // 提取扩展名
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            String extension = url.substring(lastDotIndex + 1).toLowerCase();
            
            // 验证是否为支持的图片格式
            for (String supportedExt : SUPPORTED_EXTENSIONS) {
                if (supportedExt.equals(extension)) {
                    return extension;
                }
            }
        }

        return "";
    }

    /**
     * 验证文件是否为图片格式
     * 
     * @param filePath 文件路径
     * @return 是否为图片格式
     */
    public static boolean isImageFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return false;
        }

        String extension = getFileExtension(filePath);
        for (String supportedExt : SUPPORTED_EXTENSIONS) {
            if (supportedExt.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 生成唯一的图片文件名
     * 
     * @param originalUrl 原始URL
     * @param prefix 前缀
     * @return 唯一文件名
     */
    public static String generateUniqueImageFileName(String originalUrl, String prefix) {
        String extension = getFileExtensionFromUrl(originalUrl);
        if (StringUtils.isBlank(extension)) {
            extension = "jpg";
        }
        
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        
        return prefix + "_" + timestamp + "_" + random + "." + extension;
    }

    /**
     * 复制本地图片文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否复制成功
     */
    public static boolean copyImageFile(String sourcePath, String targetPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            Files.createDirectories(target.getParent());
            
            // 复制文件
            Files.copy(source, target);
            
            log.debug("图片复制成功: {} -> {}", sourcePath, targetPath);
            return true;
        } catch (Exception e) {
            log.error("复制图片失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }

    /**
     * 获取图片文件大小（字节）
     * 
     * @param filePath 文件路径
     * @return 文件大小，失败返回-1
     */
    public static long getImageFileSize(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                return file.length();
            }
        } catch (Exception e) {
            log.error("获取图片文件大小失败: {}", filePath, e);
        }
        return -1;
    }

    /**
     * 删除图片文件
     * 
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteImageFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                return file.delete();
            }
            return true;
        } catch (Exception e) {
            log.error("删除图片文件失败: {}", filePath, e);
            return false;
        }
    }
}
